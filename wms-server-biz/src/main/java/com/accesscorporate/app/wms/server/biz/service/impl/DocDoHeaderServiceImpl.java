package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.converter.DocDoHeaderMapStruct;
import com.accesscorporate.app.wms.server.biz.manager.impl.DocDoHeaderManager;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoHeaderResponse;
import com.accesscorporate.app.wms.server.biz.service.DocDoHeaderService;
import com.accesscorporate.app.wms.server.dal.entity.DocDoHeader;
import com.accesscorporate.app.wms.server.dal.mapper.DocDoHeaderMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.accesscorporate.app.wms.server.common.utils.UserContextAssistant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 发货单头表Service实现类
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class DocDoHeaderServiceImpl extends ServiceImpl<DocDoHeaderMapper, DocDoHeader> implements DocDoHeaderService {

    @Autowired
    private DocDoHeaderManager docDoHeaderManager;

    @Override
    public List<DocDoHeaderResponse> listAll() {
        LambdaQueryWrapper<DocDoHeader> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        wrapper.eq(DocDoHeader::getIsDeleted, Boolean.FALSE);
        wrapper.orderByDesc(DocDoHeader::getCreateTime);
        return docDoHeaderManager.list(wrapper).stream()
                .map(DocDoHeaderMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public DocDoHeaderResponse get(Long id) {
        DocDoHeader docDoHeader = super.getById(id);
        return DocDoHeaderMapStruct.INSTANCE.convertToResponse(docDoHeader);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(DocDoHeaderSaveRequest request) {
        DocDoHeader docDoHeader = DocDoHeaderMapStruct.INSTANCE.convertToEntity(request);
        docDoHeader.setWarehouseId(UserContextAssistant.getCurrentWarehouseId());
        return super.save(docDoHeader);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    public Page<DocDoHeaderResponse> page(DocDoHeaderPageQueryRequest request) {
        Page<DocDoHeader> page = new Page<>(request.getCurrent(), request.getSize());
        LambdaQueryWrapper<DocDoHeader> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId());
        wrapper.eq(DocDoHeader::getIsDeleted, Boolean.FALSE);

        // 发货单号
        if (StringUtils.hasText(request.getDoNo())) {
            wrapper.like(DocDoHeader::getDoNo, request.getDoNo());
        }
        // 订单状态
        if (StringUtils.hasText(request.getStatus())) {
            wrapper.eq(DocDoHeader::getStatus, request.getStatus());
        }
        // 订单类型
        if (StringUtils.hasText(request.getDoType())) {
            wrapper.eq(DocDoHeader::getDoType, request.getDoType());
        }
        // 收货方
        if (StringUtils.hasText(request.getConsigneeName())) {
            wrapper.like(DocDoHeader::getConsigneeName, request.getConsigneeName());
        }
        // 手机号
        if (StringUtils.hasText(request.getMobile())) {
            wrapper.like(DocDoHeader::getMobile, request.getMobile());
        }
        // 运单号
        if (StringUtils.hasText(request.getTrackingNo())) {
            wrapper.like(DocDoHeader::getTrackingNo, request.getTrackingNo());
        }
        // 配送公司ID
        if (request.getCarrierId() != null) {
            wrapper.eq(DocDoHeader::getCarrierId, request.getCarrierId());
        }
        // 波次ID
        if (request.getWaveId() != null) {
            wrapper.eq(DocDoHeader::getWaveId, request.getWaveId());
        }
        // 原始ID
        if (StringUtils.hasText(request.getOrigId())) {
            wrapper.like(DocDoHeader::getOrigId, request.getOrigId());
        }
        // 供应商ID
        if (request.getSupplierId() != null) {
            wrapper.eq(DocDoHeader::getSupplierId, request.getSupplierId());
        }
        // 商家ID
        if (request.getMerchantId() != null) {
            wrapper.eq(DocDoHeader::getMerchantId, request.getMerchantId());
        }
        // 店铺ID
        if (request.getShopId() != null) {
            wrapper.eq(DocDoHeader::getShopId, request.getShopId());
        }
        // 企业客户ID
        if (request.getBusinessCustomerId() != null) {
            wrapper.eq(DocDoHeader::getBusinessCustomerId, request.getBusinessCustomerId());
        }
        // 原始SO编码
        if (StringUtils.hasText(request.getOriginalSoCode())) {
            wrapper.like(DocDoHeader::getOriginalSoCode, request.getOriginalSoCode());
        }
        // 订单子类型
        if (StringUtils.hasText(request.getOrderSubType())) {
            wrapper.eq(DocDoHeader::getOrderSubType, request.getOrderSubType());
        }
        // 订单来源系统
        if (StringUtils.hasText(request.getSourceSystem())) {
            wrapper.eq(DocDoHeader::getSourceSystem, request.getSourceSystem());
        }
        // 渠道编码
        if (StringUtils.hasText(request.getChannelCode())) {
            wrapper.eq(DocDoHeader::getChannelCode, request.getChannelCode());
        }
        // 店铺编码
        if (StringUtils.hasText(request.getStoreCode())) {
            wrapper.eq(DocDoHeader::getStoreCode, request.getStoreCode());
        }
        // 创建时间范围
        if (request.getCreateTimeStart() != null) {
            wrapper.ge(DocDoHeader::getCreateTime, request.getCreateTimeStart());
        }
        if (request.getCreateTimeEnd() != null) {
            wrapper.le(DocDoHeader::getCreateTime, request.getCreateTimeEnd());
        }
        // DO创建时间范围
        if (request.getDoCreateTimeStart() != null) {
            wrapper.ge(DocDoHeader::getDoCreateTime, request.getDoCreateTimeStart());
        }
        if (request.getDoCreateTimeEnd() != null) {
            wrapper.le(DocDoHeader::getDoCreateTime, request.getDoCreateTimeEnd());
        }
        // 发运时间范围
        if (request.getShipTimeStart() != null) {
            wrapper.ge(DocDoHeader::getShipTime, request.getShipTimeStart());
        }
        if (request.getShipTimeEnd() != null) {
            wrapper.le(DocDoHeader::getShipTime, request.getShipTimeEnd());
        }

        wrapper.orderByDesc(DocDoHeader::getCreateTime);

        Page<DocDoHeader> docDoHeaderPage = super.page(page, wrapper);
        List<DocDoHeaderResponse> responses = docDoHeaderPage.getRecords().stream()
                .map(DocDoHeaderMapStruct.INSTANCE::convertToResponse)
                .collect(Collectors.toList());
        return new Page<DocDoHeaderResponse>(docDoHeaderPage.getCurrent(), docDoHeaderPage.getSize(), docDoHeaderPage.getTotal())
                .setRecords(responses);
    }

    @Override
    public DocDoHeader queryByDoNo(String doNo) {
        return lambdaQuery()
                .eq(DocDoHeader::getDoNo, doNo)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .one();
    }

    @Override
    public DocDoHeader queryByOrigId(String origId) {
        return lambdaQuery()
                .eq(DocDoHeader::getOrigId, origId)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .one();
    }

    @Override
    public List<DocDoHeader> queryByTrackingNo(String trackingNo) {
        return lambdaQuery()
                .eq(DocDoHeader::getTrackingNo, trackingNo)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .list();
    }

    @Override
    public List<DocDoHeader> queryByMobile(String mobile) {
        return lambdaQuery()
                .eq(DocDoHeader::getMobile, mobile)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoHeader::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoHeader> queryByWaveId(Long waveId) {
        return lambdaQuery()
                .eq(DocDoHeader::getWaveId, waveId)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .list();
    }

    @Override
    public List<DocDoHeader> queryByStatus(String status) {
        return lambdaQuery()
                .eq(DocDoHeader::getStatus, status)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoHeader::getCreateTime)
                .list();
    }

    @Override
    public List<DocDoHeader> queryByDoType(String doType) {
        return lambdaQuery()
                .eq(DocDoHeader::getDoType, doType)
                .eq(DocDoHeader::getWarehouseId, UserContextAssistant.getCurrentWarehouseId())
                .eq(DocDoHeader::getIsDeleted, Boolean.FALSE)
                .orderByDesc(DocDoHeader::getCreateTime)
                .list();
    }
}
