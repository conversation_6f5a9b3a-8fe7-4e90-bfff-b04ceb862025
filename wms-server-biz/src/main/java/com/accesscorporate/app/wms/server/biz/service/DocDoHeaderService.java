package com.accesscorporate.app.wms.server.biz.service;

import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderPageQueryRequest;
import com.accesscorporate.app.wms.server.biz.params.request.DocDoHeaderSaveRequest;
import com.accesscorporate.app.wms.server.biz.params.response.DocDoHeaderResponse;
import com.accesscorporate.app.wms.server.dal.entity.DocDoHeader;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 发货单头表Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface DocDoHeaderService extends IService<DocDoHeader> {

    /**
     * 查询所有发货单
     */
    List<DocDoHeaderResponse> listAll();

    /**
     * 根据ID查询发货单
     */
    DocDoHeaderResponse get(Long id);

    /**
     * 保存发货单
     */
    boolean save(DocDoHeaderSaveRequest saveRequest);

    /**
     * 根据ID删除发货单
     */
    boolean removeById(Long id);

    /**
     * 分页查询发货单
     */
    Page<DocDoHeaderResponse> page(DocDoHeaderPageQueryRequest request);

    /**
     * 根据发货单号查询
     */
    DocDoHeader queryByDoNo(String doNo);

    /**
     * 根据原始ID查询
     */
    DocDoHeader queryByOrigId(String origId);

    /**
     * 根据运单号查询
     */
    List<DocDoHeader> queryByTrackingNo(String trackingNo);

    /**
     * 根据手机号查询
     */
    List<DocDoHeader> queryByMobile(String mobile);

    /**
     * 根据波次ID查询
     */
    List<DocDoHeader> queryByWaveId(Long waveId);

    /**
     * 根据状态查询
     */
    List<DocDoHeader> queryByStatus(String status);

    /**
     * 根据订单类型查询
     */
    List<DocDoHeader> queryByDoType(String doType);
}
